<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Fire - Daily Rewards</title>
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Rajdhani', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            color: #fff;
            overflow-x: hidden;
        }
        
        .bg-video {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
            background: url('https://wallpaperaccess.com/full/1267084.jpg') center/cover;
            filter: brightness(0.2);
        }
        
        .header {
            background: linear-gradient(135deg, rgba(0,0,0,0.9), rgba(20,20,40,0.95));
            padding: 20px;
            border-bottom: 2px solid #ff6b35;
            backdrop-filter: blur(10px);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
        }
        
        .user-details h3 {
            font-size: 18px;
            margin-bottom: 5px;
        }
        
        .user-details p {
            color: #ff6b35;
            font-size: 14px;
        }
        
        .diamonds {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(255, 107, 53, 0.2);
            padding: 10px 20px;
            border-radius: 25px;
            border: 1px solid #ff6b35;
        }
        
        .diamond-icon {
            width: 24px;
            height: 24px;
            background: #00d4ff;
            clip-path: polygon(50% 0%, 0% 50%, 50% 100%, 100% 50%);
        }
        
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .rewards-title {
            text-align: center;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff6b35, #f7931e, #ffeb3b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .rewards-subtitle {
            text-align: center;
            color: #ccc;
            margin-bottom: 40px;
            font-size: 18px;
        }
        
        .rewards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .reward-card {
            background: linear-gradient(145deg, rgba(0,0,0,0.8), rgba(20,20,40,0.9));
            border: 2px solid #333;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .reward-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }
        
        .reward-card:hover::before {
            left: 100%;
        }
        
        .reward-card:hover {
            transform: translateY(-5px);
            border-color: #ff6b35;
            box-shadow: 0 15px 30px rgba(255, 107, 53, 0.3);
        }
        
        .reward-card.legendary {
            border-color: #ffd700;
            background: linear-gradient(145deg, rgba(255,215,0,0.1), rgba(255,140,0,0.1));
        }
        
        .reward-card.legendary:hover {
            border-color: #ffd700;
            box-shadow: 0 15px 30px rgba(255, 215, 0, 0.4);
        }
        
        .reward-card.epic {
            border-color: #9c27b0;
            background: linear-gradient(145deg, rgba(156,39,176,0.1), rgba(233,30,99,0.1));
        }
        
        .reward-card.epic:hover {
            border-color: #9c27b0;
            box-shadow: 0 15px 30px rgba(156, 39, 176, 0.4);
        }
        
        .reward-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
        }
        
        .reward-card.legendary .reward-icon {
            background: linear-gradient(135deg, #ffd700, #ffb300);
        }
        
        .reward-card.epic .reward-icon {
            background: linear-gradient(135deg, #9c27b0, #e91e63);
        }
        
        .reward-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .reward-description {
            color: #ccc;
            font-size: 14px;
            margin-bottom: 20px;
            line-height: 1.4;
        }
        
        .reward-value {
            background: rgba(255, 107, 53, 0.2);
            color: #ff6b35;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 15px;
        }
        
        .claim-btn {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-family: 'Rajdhani', sans-serif;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .claim-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 107, 53, 0.4);
        }
        
        .claim-btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .claimed {
            background: #4caf50 !important;
        }
        
        .claimed:hover {
            box-shadow: 0 8px 20px rgba(76, 175, 80, 0.4);
        }
        
        .logout-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(244, 67, 54, 0.8);
            border: 1px solid #f44336;
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-family: 'Rajdhani', sans-serif;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .logout-btn:hover {
            background: #f44336;
            transform: translateY(-2px);
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 20px;
            }
            
            .rewards-title {
                font-size: 28px;
            }
            
            .rewards-grid {
                grid-template-columns: 1fr;
            }
            
            .main-content {
                padding: 20px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="bg-video"></div>

    <div class="header">
        <div class="header-content">
            <div class="user-info">
                <div class="user-avatar" id="userAvatar">👤</div>
                <div class="user-details">
                    <h3 id="userName">Player</h3>
                    <p id="userProvider">Guest Account</p>
                </div>
            </div>
            <div class="diamonds">
                <div class="diamond-icon"></div>
                <span id="diamondCount">1,250</span>
            </div>
        </div>
    </div>

    <button class="logout-btn" onclick="logout()">Logout</button>

    <div class="main-content">
        <h1 class="rewards-title">DAILY REWARDS</h1>
        <p class="rewards-subtitle">Choose your reward and claim it now!</p>

        <div class="rewards-grid">
            <div class="reward-card legendary" onclick="claimReward(this, 'legendary-skin')">
                <div class="reward-icon">👑</div>
                <h3 class="reward-title">Legendary Skin</h3>
                <p class="reward-description">Exclusive legendary character skin with special effects</p>
                <div class="reward-value">Worth 2,000 Diamonds</div>
                <button class="claim-btn">Claim Now</button>
            </div>

            <div class="reward-card epic" onclick="claimReward(this, 'weapon-skin')">
                <div class="reward-icon">🔫</div>
                <h3 class="reward-title">Epic Weapon Skin</h3>
                <p class="reward-description">Rare weapon skin with enhanced visual effects</p>
                <div class="reward-value">Worth 1,500 Diamonds</div>
                <button class="claim-btn">Claim Now</button>
            </div>

            <div class="reward-card" onclick="claimReward(this, 'diamonds')">
                <div class="reward-icon">💎</div>
                <h3 class="reward-title">Free Diamonds</h3>
                <p class="reward-description">Get free diamonds to purchase premium items</p>
                <div class="reward-value">500 Diamonds</div>
                <button class="claim-btn">Claim Now</button>
            </div>

            <div class="reward-card" onclick="claimReward(this, 'gold')">
                <div class="reward-icon">🪙</div>
                <h3 class="reward-title">Gold Coins</h3>
                <p class="reward-description">Use gold coins to upgrade your weapons and characters</p>
                <div class="reward-value">10,000 Gold</div>
                <button class="claim-btn">Claim Now</button>
            </div>

            <div class="reward-card epic" onclick="claimReward(this, 'pet')">
                <div class="reward-icon">🐾</div>
                <h3 class="reward-title">Epic Pet</h3>
                <p class="reward-description">Powerful pet companion with special abilities</p>
                <div class="reward-value">Worth 1,200 Diamonds</div>
                <button class="claim-btn">Claim Now</button>
            </div>

            <div class="reward-card" onclick="claimReward(this, 'emotes')">
                <div class="reward-icon">😎</div>
                <h3 class="reward-title">Emote Pack</h3>
                <p class="reward-description">Collection of exclusive emotes and expressions</p>
                <div class="reward-value">Worth 800 Diamonds</div>
                <button class="claim-btn">Claim Now</button>
            </div>

            <div class="reward-card legendary" onclick="claimReward(this, 'vehicle')">
                <div class="reward-icon">🏎️</div>
                <h3 class="reward-title">Legendary Vehicle</h3>
                <p class="reward-description">Exclusive vehicle skin with boost effects</p>
                <div class="reward-value">Worth 2,500 Diamonds</div>
                <button class="claim-btn">Claim Now</button>
            </div>

            <div class="reward-card" onclick="claimReward(this, 'voucher')">
                <div class="reward-icon">🎫</div>
                <h3 class="reward-title">Weapon Voucher</h3>
                <p class="reward-description">Redeem for any weapon of your choice</p>
                <div class="reward-value">Premium Voucher</div>
                <button class="claim-btn">Claim Now</button>
            </div>
        </div>
    </div>

    <script>
        let userLogin = null;
        let claimedRewards = [];

        // Initialize page
        window.addEventListener('load', function() {
            // Check if user is logged in
            const loginData = sessionStorage.getItem('ffLogin');
            if (!loginData) {
                window.location.href = 'test.html';
                return;
            }

            userLogin = JSON.parse(loginData);
            updateUserInfo();
        });

        function updateUserInfo() {
            if (userLogin) {
                document.getElementById('userName').textContent = userLogin.username.split('@')[0] || 'Player';
                document.getElementById('userProvider').textContent = `${userLogin.provider} Account`;

                // Set avatar based on provider
                const avatar = document.getElementById('userAvatar');
                if (userLogin.provider === 'Google') {
                    avatar.textContent = '🔵';
                } else if (userLogin.provider === 'Facebook') {
                    avatar.textContent = '🔷';
                } else {
                    avatar.textContent = '👤';
                }
            }
        }

        function claimReward(cardElement, rewardType) {
            const button = cardElement.querySelector('.claim-btn');

            if (button.disabled) return;

            // Disable button and show claiming state
            button.disabled = true;
            button.textContent = 'Claiming...';

            // Simulate claim process
            setTimeout(() => {
                button.textContent = 'Claimed!';
                button.classList.add('claimed');
                claimedRewards.push(rewardType);

                // Update diamond count
                const currentDiamonds = parseInt(document.getElementById('diamondCount').textContent.replace(',', ''));
                let newDiamonds = currentDiamonds;

                switch(rewardType) {
                    case 'diamonds':
                        newDiamonds += 500;
                        break;
                    case 'legendary-skin':
                    case 'weapon-skin':
                    case 'pet':
                    case 'vehicle':
                        newDiamonds += 100; // Bonus diamonds for claiming premium items
                        break;
                }

                document.getElementById('diamondCount').textContent = newDiamonds.toLocaleString();

                // Show success animation
                cardElement.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    cardElement.style.transform = '';
                }, 300);

            }, 1500);
        }

        function logout() {
            sessionStorage.removeItem('ffLogin');
            window.location.href = 'test.html';
        }
    </script>
</body>
</html>
